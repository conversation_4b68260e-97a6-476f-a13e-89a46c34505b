import { defineStore } from 'pinia'

export type ChatWidth = 'narrow' | 'default' | 'wide' | 'full'

interface ChatWidthState {
  chatWidth: ChatWidth
}

export const useChatWidthStore = defineStore('chatWidth', {
  state: (): ChatWidthState => ({
    chatWidth: 'default'
  }),

  actions: {
    setChatWidth(width: ChatWidth) {
      this.chatWidth = width
      
      // Save to localStorage
      if (process.client) {
        localStorage.setItem('chat-width', width)
      }
    },

    initializeChatWidth() {
      if (process.client) {
        const stored = localStorage.getItem('chat-width') as ChatWidth
        if (stored && ['narrow', 'default', 'wide', 'full'].includes(stored)) {
          this.chatWidth = stored
        }
      }
    }
  }
})

export const getChatWidthClass = (width: ChatWidth): string => {
  switch (width) {
    case 'narrow':
      return 'max-w-2xl'
    case 'default':
      return 'max-w-4xl'
    case 'wide':
      return 'max-w-6xl'
    case 'full':
      return 'max-w-none'
    default:
      return 'max-w-4xl'
  }
}
