import { defineStore } from 'pinia'

export type ThemeMode = 'light' | 'dark' | 'system'

interface ThemeState {
  currentMode: ThemeMode
  systemPreference: 'light' | 'dark'
}

export const useThemeStore = defineStore('theme', {
  state: (): ThemeState => ({
    currentMode: 'system',
    systemPreference: 'light'
  }),

  getters: {
    resolvedTheme: (state) => {
      if (state.currentMode === 'system') {
        return state.systemPreference
      }
      return state.currentMode
    }
  },

  actions: {
    setTheme(mode: ThemeMode) {
      this.currentMode = mode
      this.applyTheme()
    },

    setSystemPreference(preference: 'light' | 'dark') {
      this.systemPreference = preference
      if (this.currentMode === 'system') {
        this.applyTheme()
      }
    },

    applyTheme() {
      const theme = this.resolvedTheme
      const root = document.documentElement
      
      if (theme === 'dark') {
        root.classList.add('dark')
      } else {
        root.classList.remove('dark')
      }
      
      // Store preference
      if (process.client) {
        localStorage.setItem('theme', this.currentMode)
      }
    },

    initializeTheme() {
      if (process.client) {
        // Get stored preference
        const stored = localStorage.getItem('theme') as ThemeMode
        if (stored && ['light', 'dark', 'system'].includes(stored)) {
          this.currentMode = stored
        }

        // Detect system preference
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        this.systemPreference = mediaQuery.matches ? 'dark' : 'light'

        // Listen for system preference changes
        mediaQuery.addEventListener('change', (e) => {
          this.setSystemPreference(e.matches ? 'dark' : 'light')
        })

        this.applyTheme()
      }
    }
  }
})
