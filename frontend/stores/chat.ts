import { defineStore } from 'pinia'
import { nanoid } from 'nanoid'

export interface UploadedFile {
  key: string
  fileName: string
  fileType: string
  fileSize: number
  uploadedAt: number
}

interface ChatState {
  threadId: string | undefined
  uploadedFiles: UploadedFile[]
  rerenderTrigger: string
  lastProcessedDataIndex: number
  shouldUpdateQuery: boolean
  skipNextDataCheck: boolean
  attachedStreamIds: Record<string, string>
  pendingStreams: Record<string, boolean>
  targetFromMessageId: string | undefined
  targetMode: 'normal' | 'edit' | 'retry'
  uploading: boolean
}

export const useChatStore = defineStore('chat', {
  state: (): ChatState => ({
    threadId: undefined,
    uploadedFiles: [],
    rerenderTrigger: nanoid(),
    lastProcessedDataIndex: 0,
    shouldUpdateQuery: false,
    skipNextDataCheck: false,
    attachedStreamIds: {},
    pendingStreams: {},
    targetFromMessageId: undefined,
    targetMode: 'normal',
    uploading: false
  }),

  actions: {
    addUploadedFile(file: UploadedFile) {
      this.uploadedFiles.push(file)
    },

    removeUploadedFile(key: string) {
      this.uploadedFiles = this.uploadedFiles.filter(file => file.key !== key)
    },

    setUploading(uploading: boolean) {
      this.uploading = uploading
    },

    setThreadId(threadId: string | undefined) {
      this.threadId = threadId
    },

    resetChat() {
      this.uploadedFiles = []
      this.rerenderTrigger = nanoid()
      this.lastProcessedDataIndex = 0
      this.shouldUpdateQuery = false
      this.skipNextDataCheck = false
      this.attachedStreamIds = {}
      this.pendingStreams = {}
      this.targetFromMessageId = undefined
      this.targetMode = 'normal'
    },

    triggerRerender() {
      this.rerenderTrigger = nanoid()
    },

    setLastProcessedDataIndex(index: number) {
      this.lastProcessedDataIndex = index
    },

    setShouldUpdateQuery(should: boolean) {
      this.shouldUpdateQuery = should
    },

    setSkipNextDataCheck(skip: boolean) {
      this.skipNextDataCheck = skip
    },

    addAttachedStreamId(messageId: string, streamId: string) {
      this.attachedStreamIds[messageId] = streamId
    },

    removeAttachedStreamId(messageId: string) {
      delete this.attachedStreamIds[messageId]
    },

    setPendingStream(streamId: string, pending: boolean) {
      this.pendingStreams[streamId] = pending
    },

    setTargetFromMessageId(messageId: string | undefined) {
      this.targetFromMessageId = messageId
    },

    setTargetMode(mode: 'normal' | 'edit' | 'retry') {
      this.targetMode = mode
    }
  }
})
