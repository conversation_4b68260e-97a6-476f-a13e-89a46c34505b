import { defineStore } from 'pinia'

export type ReasoningEffort = 'low' | 'medium' | 'high'

export interface ModelState {
  selectedModel: string | null
  enabledTools: string[]
  selectedImageSize: string | null
  reasoningEffort: ReasoningEffort
}

// Mock models data - in real app this would come from API
export const MODELS_SHARED = [
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    shortName: 'GPT-4o',
    provider: 'openai',
    mode: 'chat' as const,
    abilities: ['vision', 'code_interpreter', 'web_search'],
    supportedImageSizes: ['1024x1024', '1792x1024', '1024x1792']
  },
  {
    id: 'claude-3-5-sonnet',
    name: 'Claude 3.5 Sonnet',
    shortName: 'Claude 3.5',
    provider: 'anthropic',
    mode: 'chat' as const,
    abilities: ['vision', 'code_interpreter'],
    supportedImageSizes: ['1024x1024']
  },
  {
    id: 'gemini-pro',
    name: '<PERSON> Pro',
    shortName: 'Gemini',
    provider: 'google',
    mode: 'chat' as const,
    abilities: ['vision'],
    supportedImageSizes: ['1024x1024']
  }
]

export const useModelStore = defineStore('model', {
  state: (): ModelState => ({
    selectedModel: null,
    enabledTools: [],
    selectedImageSize: '1024x1024',
    reasoningEffort: 'medium'
  }),

  getters: {
    selectedModelData: (state) => {
      if (!state.selectedModel) return null
      return MODELS_SHARED.find(model => model.id === state.selectedModel)
    },

    isImageModel: (state) => {
      const model = MODELS_SHARED.find(m => m.id === state.selectedModel)
      return model?.mode === 'image'
    },

    modelSupportsVision: (state) => {
      const model = MODELS_SHARED.find(m => m.id === state.selectedModel)
      return model?.abilities.includes('vision') || false
    }
  },

  actions: {
    setSelectedModel(modelId: string) {
      this.selectedModel = modelId
      // Auto-select first available image size for the model
      const model = MODELS_SHARED.find(m => m.id === modelId)
      if (model?.supportedImageSizes?.length) {
        this.selectedImageSize = model.supportedImageSizes[0]
      }
    },

    setEnabledTools(tools: string[]) {
      this.enabledTools = tools
    },

    toggleTool(tool: string) {
      const index = this.enabledTools.indexOf(tool)
      if (index > -1) {
        this.enabledTools.splice(index, 1)
      } else {
        this.enabledTools.push(tool)
      }
    },

    setSelectedImageSize(size: string) {
      this.selectedImageSize = size
    },

    setReasoningEffort(effort: ReasoningEffort) {
      this.reasoningEffort = effort
    }
  }
})
