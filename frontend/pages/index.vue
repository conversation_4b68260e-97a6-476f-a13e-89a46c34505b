<template>
  <div class="min-h-screen bg-background text-foreground">
    <NuxtRouteAnnouncer />

    <!-- Show auth prompt if not authenticated -->
    <div v-if="!isAuthenticated" class="grid min-h-screen grid-cols-1 lg:grid-cols-2">
      <!-- Left side - Background Image -->
      <div class="hidden bg-[url('/bg-light.jpg')] bg-center bg-cover bg-no-repeat lg:block dark:bg-[url('/bg-night.jpg')]" />

      <!-- Right side - Auth Content -->
      <div class="relative flex flex-col items-center justify-center gap-4 p-4 sm:p-6 md:p-8">
        <!-- Theme switcher in top right -->
        <div class="absolute top-4 right-4 sm:top-6 sm:right-6">
          <ThemeSwitcher />
        </div>

        <div class="flex w-full max-w-sm items-center justify-center gap-4 sm:max-w-md lg:max-w-lg">
          <SignupMessagePrompt />
        </div>

        <!-- Footer content -->
        <div class="absolute right-4 bottom-4 left-4 flex flex-col items-center gap-2 sm:right-6 sm:bottom-6 sm:left-6">
          <p class="text-xs text-muted-foreground text-center">
            By continuing, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </div>
    </div>

    <!-- Chat interface for authenticated users -->
    <SidebarProvider v-else>
      <ThreadsSidebar />
      <SidebarInset>
        <div
          class="flex min-h-svh flex-col"
          style="
            background-image: url('https://t3.chat/images/noise.png');
            background-repeat: repeat;
            background-size: auto;
          "
        >
          <Header :thread-id="currentThreadId" />
          <Chat />
        </div>
      </SidebarInset>
    </SidebarProvider>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'

// Page meta for SEO
definePageMeta({
  title: 'Welcome to intern3.chat',
  description: 'AI-powered chat application'
})

// Mock authentication state - in real app this would come from auth store/composable
const isAuthenticated = ref(false)
const currentThreadId = ref<string | null>(null)

// Initialize theme
const themeStore = useThemeStore()

onMounted(() => {
  themeStore.initializeTheme()

  // Mock authentication check - in real app you'd check actual auth state
  // For demo purposes, let's show the chat interface directly
  // Set to false to see the auth prompt, true to see the chat
  isAuthenticated.value = true
})
</script>
