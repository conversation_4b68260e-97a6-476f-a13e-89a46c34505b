<template>
  <main class="grid min-h-screen grid-cols-1 lg:grid-cols-2">
    <!-- Left side - Background Image -->
    <div class="hidden bg-[url('/bg-light.jpg')] bg-center bg-cover bg-no-repeat lg:block dark:bg-[url('/bg-night.jpg')]" />

    <!-- Right side - Auth Content -->
    <div class="relative flex flex-col items-center justify-center gap-4 p-4 sm:p-6 md:p-8">
      <!-- Back button -->
      <div class="absolute top-4 left-4 sm:top-6 sm:left-6">
        <NuxtLink to="/">
          <Button
            variant="ghost"
            size="sm"
            class="gap-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft class="h-4 w-4" />
            <span class="hidden sm:inline">Back</span>
          </Button>
        </NuxtLink>
      </div>
      
      <!-- Theme switcher -->
      <div class="absolute top-4 right-4 sm:top-6 sm:right-6">
        <ThemeSwitcher />
      </div>
      
      <!-- Auth card -->
      <div class="flex w-full max-w-sm items-center justify-center gap-4 sm:max-w-md lg:max-w-lg">
        <AuthCard />
      </div>
      
      <!-- Footer -->
      <div class="absolute right-4 bottom-4 left-4 flex flex-col items-center gap-2 sm:right-6 sm:bottom-6 sm:left-6">
        <p class="text-xs text-muted-foreground text-center">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </p>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
import { ArrowLeft } from 'lucide-vue-next'

const route = useRoute()
const pathname = route.params.pathname as string

definePageMeta({
  title: 'Sign In - intern3.chat'
})
</script>