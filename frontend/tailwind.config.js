/**
 * Tailwind CSS v4 configuration
 * Using v4, most configuration (themes, plugins) can be done directly in CSS
 * via `@theme` and `@plugin` directives. Since this project already uses
 * `@import "tailwindcss";` and `@plugin "tailwindcss-animate";` in
 * `assets/css/main.css`, we can keep the JS config empty.
 *
 * Keeping this file as an empty ESM export avoids CommonJS `require(...)`
 * errors in an ESM project ("type": "module").
 */
export default {
  // Intentionally left blank for Tailwind v4.
};
