<template>
  <div
    :class="cn(
      'relative flex items-center justify-between gap-4 rounded-t-lg border-2 border-input bg-background/80 p-4 shadow-lg backdrop-blur-lg md:rounded-lg',
      $attrs.class
    )"
  >
    <!-- Left side - Recording indicator and waveform -->
    <div class="flex flex-1 items-center gap-4">
      <div v-if="state.isRecording" class="flex items-center gap-2">
        <div class="size-3 animate-pulse rounded-full bg-red-500" />
        <span class="font-medium text-red-500 text-sm">Recording</span>
      </div>

      <div v-if="state.isTranscribing" class="flex items-center gap-2">
        <Loader2 class="size-3 animate-spin text-primary" />
        <span class="font-medium text-primary text-sm">Transcribing...</span>
      </div>

      <Waveform :audio-level="state.audioLevel" :is-recording="state.isRecording" />
    </div>

    <!-- Center - Timer -->
    <div class="font-mono text-foreground text-lg">
      {{ formatDuration(state.recordingDuration) }}
    </div>

    <!-- Right side - Stop button -->
    <Button
      variant="default"
      size="icon"
      class="size-8 shrink-0 rounded-md"
      @click="$emit('stop')"
      :disabled="state.isTranscribing"
    >
      <Loader2 v-if="state.isTranscribing" class="size-5 animate-spin" />
      <Square v-else class="size-5 fill-current" />
    </Button>
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import { Loader2, Square } from 'lucide-vue-next'

export interface VoiceRecorderState {
  isRecording: boolean
  isTranscribing: boolean
  recordingDuration: number
  audioLevel: number
}

interface Props {
  state: VoiceRecorderState
}

interface Emits {
  (e: 'stop'): void
}

defineProps<Props>()
defineEmits<Emits>()

const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}
</script>

<script lang="ts">
// Waveform component
export default {
  components: {
    Waveform: {
      props: {
        audioLevel: { type: Number, required: true },
        isRecording: { type: Boolean, required: true }
      },
      template: `
        <div class="flex items-center gap-1">
          <div
            v-for="i in 12"
            :key="i"
            class="w-1 bg-primary/30 rounded-full transition-all duration-75"
            :style="{
              height: isRecording 
                ? Math.max(4, Math.min(24, audioLevel * 24 + Math.random() * 8)) + 'px'
                : '4px'
            }"
          />
        </div>
      `
    }
  }
}
</script>
