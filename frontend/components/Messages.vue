<template>
  <div ref="scrollRef" class="flex-1 overflow-y-auto">
    <div ref="contentRef" class="space-y-4 p-4">
      <div
        v-for="(message, index) in messages"
        :key="message.id || index"
        class="flex gap-3"
        :class="{
          'justify-end': message.role === 'user',
          'justify-start': message.role === 'assistant'
        }"
      >
        <!-- Avatar -->
        <div
          v-if="message.role === 'assistant'"
          class="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center"
        >
          <Brain class="w-4 h-4 text-primary" />
        </div>

        <!-- Message content -->
        <div
          class="max-w-[80%] rounded-lg px-4 py-2"
          :class="{
            'bg-primary text-primary-foreground': message.role === 'user',
            'bg-muted': message.role === 'assistant'
          }"
        >
          <!-- File attachments -->
          <div v-if="message.attachments?.length" class="mb-2 space-y-2">
            <div
              v-for="attachment in message.attachments"
              :key="attachment.key"
              class="flex items-center gap-2 p-2 rounded bg-background/50"
            >
              <component
                :is="getFileIcon(attachment.fileType)"
                class="w-4 h-4 text-muted-foreground"
              />
              <span class="text-sm">{{ attachment.fileName }}</span>
            </div>
          </div>

          <!-- Message text -->
          <div v-if="message.content" class="prose prose-sm max-w-none">
            <div v-html="renderMarkdown(message.content)" />
          </div>

          <!-- Loading indicator for streaming -->
          <div v-if="message.role === 'assistant' && status === 'loading' && index === messages.length - 1" class="flex items-center gap-2 mt-2">
            <Loader2 class="w-4 h-4 animate-spin" />
            <span class="text-sm opacity-70">Thinking...</span>
          </div>
        </div>

        <!-- User avatar -->
        <div
          v-if="message.role === 'user'"
          class="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center"
        >
          <User class="w-4 h-4 text-primary-foreground" />
        </div>
      </div>

      <!-- Retry button for failed messages -->
      <div v-if="status === 'error'" class="flex justify-center">
        <Button @click="$emit('retry')" variant="outline" size="sm">
          <RotateCcw class="w-4 h-4 mr-2" />
          Retry
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Brain, User, Loader2, RotateCcw, FileText, Image as ImageIcon, Code } from 'lucide-vue-next'

interface Message {
  id?: string
  role: 'user' | 'assistant'
  content: string
  attachments?: Array<{
    key: string
    fileName: string
    fileType: string
  }>
}

interface Props {
  messages: Message[]
  status?: 'loading' | 'error' | 'idle'
}

interface Emits {
  (e: 'retry'): void
  (e: 'edit-and-retry', messageId: string): void
}

defineProps<Props>()
defineEmits<Emits>()

const scrollRef = ref<HTMLElement>()
const contentRef = ref<HTMLElement>()

const getFileIcon = (fileType: string) => {
  if (fileType.startsWith('image/')) return ImageIcon
  if (fileType.includes('code') || fileType.includes('text')) return Code
  return FileText
}

const renderMarkdown = (content: string) => {
  // Simple markdown rendering - in a real app you'd use a proper markdown library
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code class="bg-muted px-1 rounded">$1</code>')
    .replace(/\n/g, '<br>')
}

// Expose refs for parent component to control scrolling
defineExpose({
  scrollRef,
  contentRef
})
</script>

<style scoped>
@reference "../assets/css/main.css";
.prose {
  color: inherit;
}

.prose code {
  @apply bg-muted px-1 py-0.5 rounded text-sm;
}

.prose strong {
  @apply font-semibold;
}

.prose em {
  @apply italic;
}
</style>
