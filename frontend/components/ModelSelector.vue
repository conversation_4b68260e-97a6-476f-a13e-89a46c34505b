<template>
  <Select v-model="selectedModel">
    <SelectTrigger
      :class="cn(
        'h-8 bg-secondary/70 font-normal text-xs backdrop-blur-lg sm:text-sm md:rounded-md',
        '!px-1.5 min-[390px]:!px-2 gap-0.5 min-[390px]:gap-2'
      )"
    >
      <div v-if="selectedModelData" class="flex items-center gap-2">
        <div class="block min-[390px]:hidden">
          <component :is="getProviderIcon(selectedModelData)" class="h-4 w-4" />
        </div>
        <span class="hidden md:hidden min-[390px]:block">
          {{ selectedModelData.shortName || selectedModelData.name }}
        </span>
        <span class="hidden md:block">{{ selectedModelData.name }}</span>
      </div>
      <ChevronDown class="ml-auto h-4 w-4" />
    </SelectTrigger>
    
    <SelectContent class="p-0 md:w-80" align="start">
      <div class="p-2">
        <div class="text-sm font-medium mb-2">Select Model</div>
        <div class="text-xs text-muted-foreground mb-3">Choose a model for your conversation</div>
        
        <div class="space-y-2">
          <div v-for="[providerKey, providerModels] in groupedModels" :key="providerKey">
            <div class="text-xs font-medium text-muted-foreground mb-1 px-2">
              {{ getProviderName(providerKey) }}
            </div>
            <div class="space-y-1">
              <div
                v-for="model in providerModels"
                :key="model.id"
                @click="selectModel(model.id)"
                class="flex items-center justify-between p-2 rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground"
                :class="{ 'bg-accent text-accent-foreground': model.id === selectedModel }"
              >
                <div class="flex items-center gap-2">
                  <component :is="getProviderIcon(model)" class="h-4 w-4" />
                  <span class="flex items-center gap-2">
                    {{ model.name }}
                    <Check v-if="model.id === selectedModel" class="h-4 w-4" />
                  </span>
                </div>
                <div class="flex gap-2">
                  <div
                    v-if="model.mode === 'image'"
                    class="bg-accent text-accent-foreground p-1 rounded-md h-5 w-5 flex items-center justify-center"
                  >
                    <ImageIcon class="h-3 w-3" />
                  </div>
                  <div
                    v-for="ability in model.abilities"
                    :key="ability"
                    class="bg-accent text-accent-foreground p-1 rounded-md h-5 w-5 flex items-center justify-center"
                  >
                    <component :is="getAbilityIcon(ability)" class="h-3 w-3" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SelectContent>
  </Select>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useModelStore, MODELS_SHARED } from '@/stores/model'
import { cn } from '@/lib/utils'
import { 
  ChevronDown, 
  Check, 
  Eye, 
  Code, 
  Search, 
  Image as ImageIcon,
  Zap,
  Brain
} from 'lucide-vue-next'

const modelStore = useModelStore()

const selectedModel = computed({
  get: () => modelStore.selectedModel,
  set: (value) => value && modelStore.setSelectedModel(value)
})

const selectedModelData = computed(() => modelStore.selectedModelData)

const groupedModels = computed(() => {
  const groups = new Map()
  
  MODELS_SHARED.forEach(model => {
    const provider = model.provider
    if (!groups.has(provider)) {
      groups.set(provider, [])
    }
    groups.get(provider).push(model)
  })
  
  return Array.from(groups.entries())
})

const selectModel = (modelId: string) => {
  modelStore.setSelectedModel(modelId)
}

const getProviderName = (provider: string) => {
  const names: Record<string, string> = {
    'openai': 'OpenAI',
    'anthropic': 'Anthropic',
    'google': 'Google',
    'fal': 'Fal.AI'
  }
  return names[provider] || provider
}

const getProviderIcon = (model: any) => {
  // Return appropriate icon component based on provider
  switch (model.provider) {
    case 'openai':
      return Zap
    case 'anthropic':
      return Brain
    case 'google':
      return Search
    default:
      return Code
  }
}

const getAbilityIcon = (ability: string) => {
  switch (ability) {
    case 'vision':
      return Eye
    case 'code_interpreter':
      return Code
    case 'web_search':
      return Search
    case 'image_generation':
      return ImageIcon
    default:
      return Code
  }
}

// Initialize with first model if none selected
onMounted(() => {
  if (!selectedModel.value && MODELS_SHARED.length > 0) {
    modelStore.setSelectedModel(MODELS_SHARED[0].id)
  }
})
</script>
