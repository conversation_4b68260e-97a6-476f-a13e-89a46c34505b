<template>
  <div class="w-full max-w-md space-y-6">
    <div class="space-y-2 text-center">
      <h1 class="text-2xl font-semibold tracking-tight">
        {{ currentTitle }}
      </h1>
      <p class="text-sm text-muted-foreground">
        {{ currentSubtitle }}
      </p>
    </div>

    <!-- Email Step -->
    <div v-if="step === 'email'" class="space-y-4">
      <form @submit.prevent="onEmailSubmit" class="space-y-4">
        <div class="space-y-2">
          <label for="email" class="text-sm font-medium">Email</label>
          <input
            id="email"
            v-model="emailForm.email"
            type="email"
            placeholder="Enter your email"
            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            required
          />
        </div>
        <Button type="submit" class="w-full" :disabled="isLoading">
          {{ isLoading ? 'Sending...' : 'Continue with Email' }}
        </Button>
      </form>

      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <span class="w-full border-t" />
        </div>
        <div class="relative flex justify-center text-xs uppercase">
          <span class="bg-background px-2 text-muted-foreground">Or continue with</span>
        </div>
      </div>

      <div class="grid grid-cols-3 gap-3">
        <Button variant="outline" @click="() => onSocialSignIn('google')" :disabled="isLoading">
          <svg class="h-4 w-4" viewBox="0 0 24 24">
            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
        </Button>
        <Button variant="outline" @click="() => onSocialSignIn('github')" :disabled="isLoading">
          <svg class="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
        </Button>
        <Button variant="outline" @click="() => onSocialSignIn('twitch')" :disabled="isLoading">
          <svg class="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
            <path d="M11.571 4.714h1.715v5.143H11.57zm4.715 0H18v5.143h-1.714zM6 0L1.714 4.286v15.428h5.143V24l4.286-4.286h3.428L22.286 12V0zm14.571 11.143l-3.428 3.428h-3.429l-3 3v-3H6.857V1.714h13.714Z"/>
          </svg>
        </Button>
      </div>
    </div>

    <!-- Skip auth for testing - auto redirect -->
    <div v-if="step === 'testing'" class="text-center space-y-4">
      <p class="text-sm text-muted-foreground">Auth skipped for testing</p>
      <Button @click="skipAuth" class="w-full">
        Continue to App
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter()
const route = useRoute()

const step = ref('testing') // Skip to testing for now
const isLoading = ref(false)

const emailForm = reactive({
  email: ''
})

const currentTitle = computed(() => {
  if (step.value === 'testing') return 'Testing Mode'
  return 'Sign in to intern3.chat'
})

const currentSubtitle = computed(() => {
  if (step.value === 'testing') return 'Authentication is disabled for testing'
  return 'Enter your email to get started'
})

const onEmailSubmit = () => {
  // Skip actual auth for testing
  skipAuth()
}

const onSocialSignIn = (provider: 'google' | 'github' | 'twitch') => {
  // Skip actual auth for testing
  skipAuth()
}

const skipAuth = () => {
  // Redirect to main app
  router.push('/')
}

// Auto-skip auth after 2 seconds for testing
onMounted(() => {
  setTimeout(() => {
    if (step.value === 'testing') {
      skipAuth()
    }
  }, 2000)
})
</script>