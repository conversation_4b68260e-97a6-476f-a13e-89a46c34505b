<template>
  <Transition
    enter-active-class="transition-all duration-100 ease-out"
    enter-from-class="opacity-0 translate-y-5 scale-90"
    enter-to-class="opacity-100 translate-y-0 scale-100"
    leave-active-class="transition-all duration-100 ease-out"
    leave-from-class="opacity-100 translate-y-0 scale-100"
    leave-to-class="opacity-0 translate-y-5 scale-90"
  >
    <div v-if="!isAtBottom">
      <Button
        @click="scrollToBottom"
        size="sm"
        variant="outline"
        class="rounded-full border bg-background/80 backdrop-blur-xl transition-all duration-200 hover:bg-background/90"
      >
        <span class="inline-block">Scroll to bottom</span>
        <ChevronDown class="mt-0.5 h-4 w-4" />
      </Button>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ChevronDown } from 'lucide-vue-next'

interface Props {
  isAtBottom: boolean
}

interface Emits {
  (e: 'scroll-to-bottom'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const scrollToBottom = () => {
  emit('scroll-to-bottom')
}
</script>
