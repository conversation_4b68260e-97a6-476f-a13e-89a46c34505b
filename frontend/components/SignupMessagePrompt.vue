<template>
  <div class="flex flex-col items-center justify-center gap-8 text-center">
    <!-- Logo -->
    <Logo />
    
    <!-- Main heading -->
    <div class="space-y-4">
      <h1 class="text-4xl font-bold tracking-tight">
        Welcome to intern3.chat
      </h1>
      <p class="text-lg text-muted-foreground max-w-md">
        AI-powered conversations that adapt to your needs. Join thousands of users already chatting smarter.
      </p>
    </div>
    
    <!-- CTA Button -->
    <MagneticButton @click="handleNavigation">
      <Button size="lg" class="text-lg px-8 py-6">
        Get Started
      </Button>
    </MagneticButton>
    
    <!-- Forest illustration -->
    <div class="mt-8 opacity-20">
      <ForestSvg class="w-64 h-32" />
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter()

const handleNavigation = () => {
  router.push('/auth/signup')
}
</script>