<template>
  <div
    class="group peer hidden md:block text-sidebar-foreground"
    :data-state="state"
    :data-collapsible="state === 'collapsed' ? collapsible : ''"
    :data-variant="variant"
    :data-side="side"
  >
    <!-- Sidebar gap on desktop -->
    <div
      :class="cn(
        'duration-200 relative h-svh bg-transparent transition-[width] ease-linear',
        'group-data-[collapsible=offcanvas]:w-0',
        'group-data-[side=right]:rotate-180',
        variant === 'floating' || variant === 'inset'
          ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+1rem)]'
          : 'group-data-[collapsible=icon]:w-[var(--sidebar-width-icon)]',
        $attrs.class
      )"
      :style="{ width: 'var(--sidebar-width)' }"
    >
      <!-- Actual sidebar content -->
      <div
        :class="cn(
          'duration-200 fixed inset-y-0 z-10 hidden h-svh transition-[left,right,width] ease-linear md:flex',
          side === 'left' ? 'left-0' : 'right-0',
          variant === 'floating' || variant === 'inset'
            ? cn(
                'p-2',
                variant === 'floating'
                  ? 'border border-sidebar-border bg-sidebar shadow-lg'
                  : 'bg-background'
              )
            : 'group-data-[collapsible=offcanvas]:w-0 bg-sidebar',
          'group-data-[collapsible=icon]:w-[var(--sidebar-width-icon)]'
        )"
        :style="{ width: 'var(--sidebar-width)' }"
      >
        <div
          :class="cn(
            'flex h-full w-full flex-col',
            variant === 'floating' || variant === 'inset' ? 'rounded-lg' : ''
          )"
        >
          <slot />
        </div>
      </div>
    </div>

    <!-- Mobile sidebar -->
    <div v-if="isMobile" class="fixed inset-0 z-50 md:hidden">
      <div
        v-if="openMobile"
        class="fixed inset-0 bg-black/50"
        @click="setOpenMobile(false)"
      />
      <div
        :class="cn(
          'fixed h-full w-[280px] bg-sidebar transition-transform duration-200 ease-in-out',
          side === 'left' ? 'left-0' : 'right-0',
          openMobile ? 'translate-x-0' : side === 'left' ? '-translate-x-full' : 'translate-x-full'
        )"
      >
        <div class="flex h-full w-full flex-col">
          <slot />
        </div>
      </div>
    </div>

    <!-- Resize rail -->
    <button
      ref="railRef"
      class="absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex cursor-ew-resize hover:after:bg-sidebar-border active:after:bg-sidebar-border"
      @mousedown="handleMouseDown"
      tabindex="-1"
      aria-label="Toggle Sidebar"
      title="Toggle Sidebar"
    />
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import { inject, ref } from 'vue'

interface Props {
  side?: 'left' | 'right'
  variant?: 'sidebar' | 'floating' | 'inset'
  collapsible?: 'offcanvas' | 'icon' | 'none'
}

withDefaults(defineProps<Props>(), {
  side: 'left',
  variant: 'sidebar',
  collapsible: 'offcanvas'
})

// Inject sidebar context
const sidebarContext = inject('sidebarContext') as any
const { state, openMobile, setOpenMobile, isMobile, toggleSidebar } = sidebarContext || {}

const railRef = ref()

const handleMouseDown = (e: MouseEvent) => {
  e.preventDefault()
  toggleSidebar?.()
}
</script>
