<template>
  <Button
    data-sidebar="trigger"
    variant="ghost"
    size="icon"
    :class="cn('h-7 w-7', $attrs.class)"
    @click="handleClick"
    v-bind="$attrs"
  >
    <PanelLeft class="h-4 w-4" />
    <span class="sr-only">Toggle Sidebar</span>
  </Button>
</template>

<script setup lang="ts">
import { inject } from 'vue'
import { PanelLeft } from 'lucide-vue-next'
import { cn } from '@/lib/utils'

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()

const sidebarContext = inject('sidebarContext') as any
const { toggleSidebar } = sidebarContext || {}

const handleClick = (event: MouseEvent) => {
  emit('click', event)
  toggleSidebar?.()
}
</script>
