<template>
  <main
    :class="cn(
      'relative flex min-h-svh flex-1 flex-col bg-background',
      'peer-data-[variant=inset]:min-h-[calc(100svh-1rem)] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm',
      $attrs.class
    )"
    v-bind="$attrs"
  >
    <slot />
  </main>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
</script>
