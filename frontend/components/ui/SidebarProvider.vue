<template>
  <div
    :style="{
      '--sidebar-width': width,
      '--sidebar-width-icon': '3rem'
    }"
    :class="cn(
      'group/sidebar-wrapper flex min-h-svh w-full has-data-[variant=inset]:bg-sidebar',
      $attrs.class
    )"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { ref, provide, computed, onMounted } from 'vue'
import { useMediaQuery } from '@vueuse/core'
import { cn } from '@/lib/utils'

interface Props {
  defaultOpen?: boolean
  open?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultOpen: true
})

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
}>()

// State
const internalOpen = ref(props.defaultOpen)
const openMobile = ref(false)
const width = ref('16rem')
const isDraggingRail = ref(false)

// Responsive
const isMobile = useMediaQuery('(max-width: 768px)')

// Computed
const open = computed({
  get: () => props.open ?? internalOpen.value,
  set: (value) => {
    if (props.open !== undefined) {
      emit('update:open', value)
    } else {
      internalOpen.value = value
    }
  }
})

const state = computed(() => {
  if (isMobile.value) return 'expanded'
  return open.value ? 'expanded' : 'collapsed'
})

// Methods
const setOpen = (value: boolean) => {
  open.value = value
}

const setOpenMobile = (value: boolean) => {
  openMobile.value = value
}

const toggleSidebar = () => {
  if (isMobile.value) {
    setOpenMobile(!openMobile.value)
  } else {
    setOpen(!open.value)
  }
}

const setWidth = (newWidth: string) => {
  width.value = newWidth
}

const setIsDraggingRail = (value: boolean) => {
  isDraggingRail.value = value
}

// Context
const sidebarContext = {
  state,
  open,
  setOpen,
  openMobile,
  setOpenMobile,
  isMobile,
  toggleSidebar,
  width,
  setWidth,
  isDraggingRail,
  setIsDraggingRail
}

provide('sidebarContext', sidebarContext)

// Initialize from localStorage
onMounted(() => {
  if (process.client) {
    const stored = localStorage.getItem('sidebar-state')
    if (stored) {
      try {
        const { open: storedOpen, width: storedWidth } = JSON.parse(stored)
        if (typeof storedOpen === 'boolean') {
          internalOpen.value = storedOpen
        }
        if (typeof storedWidth === 'string') {
          width.value = storedWidth
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }
  }
})

// Save to localStorage when state changes
watch([open, width], () => {
  if (process.client) {
    localStorage.setItem('sidebar-state', JSON.stringify({
      open: open.value,
      width: width.value
    }))
  }
}, { deep: true })
</script>
