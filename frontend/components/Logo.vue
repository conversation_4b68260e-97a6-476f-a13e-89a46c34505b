<template>
  <svg viewBox="0 0 1000 1000" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-full">
    <g clip-path="url(#clip0_1462_364)">
      <rect width="1000" height="1000" rx="500" :fill="logoBg" />
      <LogoPath :fill="logoFg" />

      <!-- Animated eyelids for blinking effect -->
      <g
        class="animate-blink"
        style="transform-origin: center"
      >
        <!-- Left eyelid -->
        <circle cx="368" cy="400" r="200" :fill="logoFg" />
        <!-- Right eyelid -->
        <circle cx="632" cy="400" r="200" :fill="logoFg" />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_1462_364">
        <rect width="1000" height="1000" rx="500" :fill="logoBg" />
      </clipPath>
    </defs>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

const logoBg = computed(() => {
  return themeStore.resolvedTheme === 'dark' ? 'hsl(var(--foreground))' : 'hsl(var(--background))'
})

const logoFg = computed(() => {
  return themeStore.resolvedTheme === 'dark' ? 'hsl(var(--background))' : 'hsl(var(--foreground))'
})
</script>

<script lang="ts">
// Logo path component
export default {
  components: {
    LogoPath: {
      props: {
        fill: { type: String, required: true }
      },
      template: `
        <path
          :fill="fill"
          d="M368 200C423.228 200 468 244.772 468 300V500C468 555.228 423.228 600 368 600C312.772 600 268 555.228 268 500V300C268 244.772 312.772 200 368 200Z M632 200C687.228 200 732 244.772 732 300V500C732 555.228 687.228 600 632 600C576.772 600 532 555.228 532 500V300C532 244.772 576.772 200 632 200Z M500 700C555.228 700 600 744.772 600 800C600 855.228 555.228 900 500 900C444.772 900 400 855.228 400 800C400 744.772 444.772 700 500 700Z"
        />
      `
    }
  }
}
</script>

<style scoped>
@keyframes blink {
  0%, 15%, 25%, 35%, 85%, 100% {
    transform: scaleY(0) translateY(30px);
  }
  20%, 30% {
    transform: scaleY(1) translateY(30px);
  }
}

.animate-blink {
  animation: blink 5s infinite;
}
</style>