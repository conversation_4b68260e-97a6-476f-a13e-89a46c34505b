<template>
  <Button
    variant="ghost"
    size="icon"
    class="h-7 w-7 rounded-full"
    @click="handleUserClick"
  >
    <div class="h-5 w-5 rounded-full bg-primary flex items-center justify-center">
      <User class="h-3 w-3 text-primary-foreground" />
    </div>
    <span class="sr-only">User menu</span>
  </Button>
</template>

<script setup lang="ts">
import { User } from 'lucide-vue-next'

const handleUserClick = () => {
  // In real app: open user menu
  console.log('User menu')
}
</script>
