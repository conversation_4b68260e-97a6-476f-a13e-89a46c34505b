<template>
  <Sidebar>
    <!-- Sidebar Header -->
    <div class="flex items-center gap-2 p-4 border-b border-sidebar-border">
      <div class="h-6 w-6 rounded-sm border">
        <Logo />
      </div>
      <span class="font-semibold text-sm">intern3.chat</span>
    </div>

    <!-- Sidebar Content -->
    <div class="flex-1 overflow-y-auto">
      <!-- New Chat Button -->
      <SidebarGroup>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton @click="handleNewChat" class="bg-primary text-primary-foreground hover:bg-primary/90">
                <Plus class="h-4 w-4" />
                <span>New Chat</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <!-- Search -->
      <SidebarGroup>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton @click="handleSearch">
                <Search class="h-4 w-4" />
                <span>Search chats</span>
                <kbd class="ml-auto text-xs bg-sidebar-accent px-1.5 py-0.5 rounded">⌘K</kbd>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <!-- Library -->
      <SidebarGroup>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton @click="handleLibrary">
                <Image class="h-4 w-4" />
                <span>Library</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <!-- Folders -->
      <SidebarGroup>
        <SidebarGroupLabel class="flex items-center justify-between">
          <span class="flex items-center gap-2">
            <Folder class="h-4 w-4" />
            Folders
          </span>
          <Button variant="ghost" size="icon" class="h-4 w-4 p-0" @click="handleAddFolder">
            <Plus class="h-3 w-3" />
          </Button>
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem v-for="folder in folders" :key="folder.id">
              <SidebarMenuButton @click="() => handleFolderClick(folder.id)">
                <Folder class="h-4 w-4" />
                <span>{{ folder.name }}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <!-- Recent Threads -->
      <SidebarGroup v-if="recentThreads.length > 0">
        <SidebarGroupLabel>
          <Clock class="h-4 w-4" />
          Recent
        </SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem v-for="thread in recentThreads" :key="thread.id">
              <SidebarMenuButton 
                @click="() => handleThreadClick(thread.id)"
                :isActive="currentThreadId === thread.id"
              >
                <MessageSquare class="h-4 w-4" />
                <span class="truncate">{{ thread.title || 'New Chat' }}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <!-- Last 7 Days -->
      <SidebarGroup v-if="lastSevenDaysThreads.length > 0">
        <SidebarGroupLabel>Last 7 Days</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem v-for="thread in lastSevenDaysThreads" :key="thread.id">
              <SidebarMenuButton 
                @click="() => handleThreadClick(thread.id)"
                :isActive="currentThreadId === thread.id"
              >
                <MessageSquare class="h-4 w-4" />
                <span class="truncate">{{ thread.title || 'New Chat' }}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <!-- Last 30 Days -->
      <SidebarGroup v-if="lastThirtyDaysThreads.length > 0">
        <SidebarGroupLabel>Last 30 Days</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem v-for="thread in lastThirtyDaysThreads" :key="thread.id">
              <SidebarMenuButton 
                @click="() => handleThreadClick(thread.id)"
                :isActive="currentThreadId === thread.id"
              >
                <MessageSquare class="h-4 w-4" />
                <span class="truncate">{{ thread.title || 'New Chat' }}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </div>
  </Sidebar>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Plus, 
  Search, 
  Image, 
  Folder, 
  Clock, 
  MessageSquare 
} from 'lucide-vue-next'

interface Thread {
  id: string
  title: string
  createdAt: Date
}

interface Folder {
  id: string
  name: string
}

// Mock data - in real app this would come from API/store
const threads = ref<Thread[]>([
  { id: '1', title: 'Novel Writing Assistance', createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2) },
  { id: '2', title: 'Code Review Help', createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2) },
  { id: '3', title: 'Recipe Ideas', createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10) },
])

const folders = ref<Folder[]>([
  { id: '1', name: 'Last 30 Days' },
  { id: '2', name: 'Novel Writing Assistance' }
])

const currentThreadId = ref<string | null>(null)

// Computed thread groups
const recentThreads = computed(() => {
  const oneDayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24)
  return threads.value.filter(thread => thread.createdAt > oneDayAgo)
})

const lastSevenDaysThreads = computed(() => {
  const oneDayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24)
  const sevenDaysAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 7)
  return threads.value.filter(thread => 
    thread.createdAt <= oneDayAgo && thread.createdAt > sevenDaysAgo
  )
})

const lastThirtyDaysThreads = computed(() => {
  const sevenDaysAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 7)
  const thirtyDaysAgo = new Date(Date.now() - 1000 * 60 * 60 * 24 * 30)
  return threads.value.filter(thread => 
    thread.createdAt <= sevenDaysAgo && thread.createdAt > thirtyDaysAgo
  )
})

// Event handlers
const handleNewChat = () => {
  currentThreadId.value = null
  // In real app: navigate to new chat
  console.log('New chat')
}

const handleSearch = () => {
  // In real app: open search modal
  console.log('Search chats')
}

const handleLibrary = () => {
  // In real app: navigate to library
  console.log('Library')
}

const handleAddFolder = () => {
  // In real app: open add folder modal
  console.log('Add folder')
}

const handleFolderClick = (folderId: string) => {
  // In real app: navigate to folder
  console.log('Folder clicked:', folderId)
}

const handleThreadClick = (threadId: string) => {
  currentThreadId.value = threadId
  // In real app: navigate to thread
  console.log('Thread clicked:', threadId)
}
</script>
