<template>
  <div 
    ref="buttonRef"
    class="relative cursor-pointer"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
    @mousemove="onMouseMove"
    @click="$emit('click')"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits<{
  click: []
}>()

const buttonRef = ref<HTMLElement>()

const onMouseEnter = () => {
  // Add magnetic effect on hover
}

const onMouseLeave = () => {
  // Reset position
}

const onMouseMove = (e: MouseEvent) => {
  // Magnetic follow effect
}
</script>