# New Stack Setup - Nuxt + Axum

This document describes the new stack setup with Nuxt.js frontend and Axum backend.

## Project Structure

```
├── frontend/          # Nuxt.js frontend application
│   ├── app/           # Nuxt app directory
│   ├── assets/        # CSS and other assets
│   ├── components/    # Vue components
│   ├── lib/           # Utility functions
│   ├── nuxt.config.ts # Nuxt configuration
│   └── package.json   # Frontend dependencies
├── api/               # Rust Axum backend API
│   ├── src/           # Rust source code
│   ├── migrations/    # Database migrations
│   ├── Cargo.toml     # Rust dependencies
│   └── docker-compose.yml # PostgreSQL setup
└── README_NEW_STACK.md # This file
```

## Frontend (Nuxt.js)

### Features
- **Nuxt 3** - Vue.js framework with SSR/SSG capabilities
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - High-quality Vue components built on Radix Vue
- **TypeScript** - Type safety throughout the application

### Setup
```bash
cd frontend
npm install
npm run dev
```

The frontend will be available at `http://localhost:3000`

### Key Files
- `nuxt.config.ts` - Nuxt configuration with Tail<PERSON> and aliases
- `assets/css/main.css` - Global styles with Tailwind directives and CSS variables
- `components.json` - shadcn/ui configuration
- `tailwind.config.js` - Tailwind configuration with shadcn/ui theme

## Backend (Axum + PostgreSQL)

### Features
- **Axum** - Modern async web framework for Rust
- **SQLx** - Async SQL toolkit with compile-time checked queries
- **PostgreSQL** - Robust relational database
- **UUID** - Primary keys using UUIDs
- **CORS** - Cross-origin resource sharing enabled
- **Tracing** - Structured logging

### Setup
1. Start PostgreSQL:
   ```bash
   cd api
   docker-compose up -d
   ```

2. Run the API server:
   ```bash
   cargo run
   ```

The API will be available at `http://localhost:3001`

### API Endpoints
- `GET /` - Health check
- `GET /api/health` - Health check with timestamp
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user

### Database Schema
- **users** table with id (UUID), email, name, created_at, updated_at

## Development Workflow

1. Start the database:
   ```bash
   cd api && docker-compose up -d
   ```

2. Start the backend:
   ```bash
   cd api && cargo run
   ```

3. Start the frontend:
   ```bash
   cd frontend && npm run dev
   ```

## Testing the Setup

### Frontend Test
```bash
cd frontend
npm run dev
```
Visit `http://localhost:3000` to see the Nuxt app with Tailwind CSS and basic styling.

### Backend Test
```bash
cd api
# Start PostgreSQL first
docker-compose up -d
# Then start the API
cargo run
```
Visit `http://localhost:3001/api/health` to see the health check endpoint.

## Next Steps

This is a basic skeleton setup. You can now:

1. Add more API endpoints and database models
2. Create Vue components using shadcn/ui
3. Set up authentication and authorization
4. Add API client in the frontend to communicate with the backend
5. Implement proper error handling and validation
6. Add tests for both frontend and backend

## Migration from React

The current setup provides a solid foundation for migrating from React to Vue/Nuxt:

- Replace React components with Vue components
- Use shadcn/ui Vue components instead of React versions
- Migrate state management (if using Redux/Zustand, consider Pinia for Vue)
- Update API calls to use the new Axum backend
- Migrate routing from React Router to Nuxt's file-based routing

## Status

✅ **Frontend**: Nuxt 3 with Tailwind CSS and shadcn/ui setup complete
✅ **Backend**: Axum server with PostgreSQL and basic CRUD endpoints complete
✅ **Database**: PostgreSQL with migrations setup complete
✅ **Build**: Both frontend and backend build successfully

The skeleton is ready for development!
